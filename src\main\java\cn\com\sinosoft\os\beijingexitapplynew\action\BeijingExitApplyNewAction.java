package cn.com.sinosoft.os.beijingexitapplynew.action;

import ie.bsp.frame.action.BaseEditAction;
import ie.bsp.frame.exception.GeneralException;
import ie.bsp.frame.model.SignInfo;
import cn.com.sinosoft.os.beijingexitapplynew.model.BeijingExitApplyNew;
import cn.com.sinosoft.os.beijingexitapplynew.service.BeijingExitApplyNewService;

/**
 * 出京申请 - Action.
 *
 * <AUTHOR>
 * @date: 2025/08/02 16:00:00
 * @version V1.0
 */
public class BeijingExitApplyNewAction extends BaseEditAction {

	/**
	 * 默认构造.
	 */
	public BeijingExitApplyNewAction() {
		moduleId = "OS1000002";
	}

	// serialVersionUID
	private static final long serialVersionUID = 1L;

	// 出京申请 - 接口.
	private BeijingExitApplyNewService service;

	// 出京申请 - 数据.
	private BeijingExitApplyNew result;
	
	private String parama; // 参数
	 
	public String getParama() {
		return parama;
	}

	public void setParama(String parama) {
		this.parama = parama;
	}
	
	private String taskid;
	
	private String piId;
	
	private SignInfo signInfo;

	// 主键.
	private String id;

	/**
	 * 获取 出京申请 - 接口.
	 *
	 * @return 出京申请 - 接口
	 */
	public BeijingExitApplyNewService getService() {
		return service;
	}

	/**
	 * 设置 出京申请 - 接口.
	 *
	 * @param service 出京申请 - 接口
	 */
	public void setService(BeijingExitApplyNewService service) {
		this.service = service;
	}

	/**
	 * 获取 出京申请 - 数据.
	 *
	 * @return 出京申请 - 数据
	 */
	public BeijingExitApplyNew getResult() {
		return result;
	}

	/**
	 * 设置 出京申请 - 数据.
	 *
	 * @param result 出京申请 - 数据
	 */
	public void setResult(BeijingExitApplyNew result) {
		this.result = result;
	}

	/**
	 * 获取 主键.
	 *
	 * @return 主键
	 */
	public String getId() {
		return id;
	}

	/**
	 * 设置 主键.
	 *
	 * @param id 主键
	 */
	public void setId(String id) {
		this.id = id;
	}

	/**
	 * 获取 taskid.
	 *
	 * @return taskid
	 */
	public String getTaskid() {
		return taskid;
	}

	/**
	 * 设置 taskid.
	 *
	 * @param taskid taskid
	 */
	public void setTaskid(String taskid) {
		this.taskid = taskid;
	}

	/**
	 * 获取 piId.
	 *
	 * @return piId
	 */
	public String getPiId() {
		return piId;
	}

	/**
	 * 设置 piId.
	 *
	 * @param piId piId
	 */
	public void setPiId(String piId) {
		this.piId = piId;
	}

	/**
	 * 获取 signInfo.
	 *
	 * @return signInfo
	 */
	public SignInfo getSignInfo() {
		return signInfo;
	}

	/**
	 * 设置 signInfo.
	 *
	 * @param signInfo signInfo
	 */
	public void setSignInfo(SignInfo signInfo) {
		this.signInfo = signInfo;
	}

	@Override
	public void doQryParentInput() throws GeneralException {
		
	}

	@Override
	public void doViewParentSubmit() throws GeneralException {
		funcId = "OS100000201";
		result = service.get(id);
		piId = result.getPiId();
	}

	@Override
	public void doDelteParentSubmit() throws GeneralException {
		funcId = "";
		service.delete(id);
	}

	@Override
	public void doAddParentInput() throws GeneralException {
		String number = service.numberGenerate(); // 生成编号
		BeijingExitApplyNew result = new BeijingExitApplyNew();
		result.setApplyCode(number);
		this.result = result;
		this.getRequest().setAttribute("number", number);
		piId = "";
	}

	@Override
	public void doAddParentSubmit() throws GeneralException {
		service.save(result);
	}

	@Override
	public void doEditParentInput() throws GeneralException {
		doViewParentSubmit();
	}

	@Override
	public void doEditParentSubmit() throws GeneralException {
		service.edit(result);
	}

	@Override
	public void doAudit() throws GeneralException {
		funcId = "";
	}
	
	/**
	 * 审批页面输入.
	 */
	public String auditParentInput(){
		result = service.getPiId(piId);
		return SUCCESS;
	}
	
	/**
	 * 申请提交.
	 */
	public void applySubmit(){
		service.appSubmit(id, parama, taskid, piId);
		methodSuccess(true); // 记录日志 并关闭窗口
   }
}
