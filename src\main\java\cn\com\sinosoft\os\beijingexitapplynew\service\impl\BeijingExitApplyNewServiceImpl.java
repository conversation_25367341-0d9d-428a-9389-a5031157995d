package cn.com.sinosoft.os.beijingexitapplynew.service.impl;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import ie.bsp.frame.dao.BaseDAO;
import ie.bsp.frame.exception.GeneralException;
import ie.bsp.frame.model.SignInfo;
import ie.bsp.frame.service.impl.BaseServiceImpl;
import ie.bsp.frame.util.SessionUtil;
import ie.weaf.toolkit.Util;
import cn.com.sinosoft.os.beijingexitapplynew.model.BeijingExitApplyNew;
import cn.com.sinosoft.os.beijingexitapplynew.service.BeijingExitApplyNewService;

/**
 * 出京申请 - service实现类.
 *
 * <AUTHOR>
 * @date: 2025/08/02 16:00:00
 * @version V1.0
 */
public class BeijingExitApplyNewServiceImpl extends BaseServiceImpl implements BeijingExitApplyNewService {

	@Override
	public BeijingExitApplyNew get(String id) {
		return (BeijingExitApplyNew) dao.get(BeijingExitApplyNew.class, id);
	}

	@Override
	public void delete(String id) {
		dao.delete(BeijingExitApplyNew.class, id);
	}

	@Override
	public void save(BeijingExitApplyNew result) {
		if (Util.isEmpty(result.getId())) {
			result.setId(Util.getUUID());
		}
		
		// 设置添加信息
		result.setAddZone(SessionUtil.getZoneCode());
		result.setAddOrg(SessionUtil.getOrgCode());
		result.setAddDep(SessionUtil.getDepCode());
		result.setAddUser(SessionUtil.getUserCode());
		result.setAddTime(new Date());
		result.setState("1"); // 有效
		
		// 自动获取科室信息
		if (Util.isEmpty(result.getDepartment())) {
			result.setDepartment(SessionUtil.getDepName());
		}
		
		dao.save(result);
	}

	@Override
	public void edit(BeijingExitApplyNew result) {
		// 设置修改信息
		result.setModyZone(SessionUtil.getZoneCode());
		result.setModyOrg(SessionUtil.getOrgCode());
		result.setModyDep(SessionUtil.getDepCode());
		result.setModyUser(SessionUtil.getUserCode());
		result.setModyTime(new Date());
		
		dao.update(result);
	}

	@Override
	public BeijingExitApplyNew getPiId(String piId) {
		String hql = "from BeijingExitApplyNew where piId = ?";
		List<BeijingExitApplyNew> list = dao.find(hql, piId);
		if (list != null && list.size() > 0) {
			return list.get(0);
		}
		return null;
	}

	@Override
	public SignInfo getSignInfo(String id) {
		BeijingExitApplyNew result = get(id);
		if (result != null) {
			SignInfo signInfo = new SignInfo();
			signInfo.setId(result.getId());
			signInfo.setPiId(result.getPiId());
			signInfo.setAuditState(result.getAuditState());
			return signInfo;
		}
		return null;
	}

	@Override
	public void appSubmit(String id, String parama, String taskid, String piId) {
		BeijingExitApplyNew result = get(id);
		if (result != null) {
			result.setPiId(piId);
			result.setAuditState("1"); // 审核中
			edit(result);
		}
	}

	@Override
	public String numberGenerate() {
		// 生成申请编号：CJ + 年月日 + 4位流水号
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		String dateStr = sdf.format(new Date());
		
		// 查询当天最大编号
		String hql = "select max(applyCode) from BeijingExitApplyNew where applyCode like ?";
		String prefix = "CJ" + dateStr;
		List<String> list = dao.find(hql, prefix + "%");
		
		int maxNum = 0;
		if (list != null && list.size() > 0 && list.get(0) != null) {
			String maxCode = list.get(0);
			if (maxCode.length() >= prefix.length() + 4) {
				try {
					String numStr = maxCode.substring(prefix.length());
					maxNum = Integer.parseInt(numStr);
				} catch (NumberFormatException e) {
					// 忽略解析错误
				}
			}
		}
		
		// 生成新编号
		maxNum++;
		String numStr = String.format("%04d", maxNum);
		return prefix + numStr;
	}
}
