<?xml version="1.0" encoding="ASCII"?>
<pi:Diagram xmi:version="2.0" xmlns:xmi="http://www.omg.org/XMI" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:al="http://eclipse.org/graphiti/mm/algorithms" xmlns:pi="http://eclipse.org/graphiti/mm/pictograms" visible="true" active="true" gridUnit="10" diagramTypeId="BPMNdiagram" name="beijingexitapply" snapToGrid="true" version="0.11.0">
  <graphicsAlgorithm xsi:type="al:Rectangle" background="//@colors.1" foreground="//@colors.0" lineWidth="1" transparency="0.0" width="1000" height="1000"/>
  <children xsi:type="pi:ContainerShape" visible="true" active="true">
    <properties key="independentObject" value="68812635"/>
    <graphicsAlgorithm xsi:type="al:Ellipse" lineWidth="1" filled="false" lineVisible="false" transparency="0.0" width="35" height="35" x="170" y="256">
      <graphicsAlgorithmChildren xsi:type="al:Ellipse" lineWidth="1" transparency="0.0" width="35" height="35" style="//@styles.0"/>
    </graphicsAlgorithm>
    <anchors xsi:type="pi:ChopboxAnchor" outgoingConnections="//@connections.5"/>
    <anchors xsi:type="pi:BoxRelativeAnchor" visible="true" active="true" referencedGraphicsAlgorithm="//@children.0/@graphicsAlgorithm/@graphicsAlgorithmChildren.0" relativeWidth="1.0" relativeHeight="0.51">
      <graphicsAlgorithm xsi:type="al:Ellipse" filled="false" lineVisible="false"/>
    </anchors>
  </children>
  <children xsi:type="pi:ContainerShape" visible="true" active="true">
    <properties key="independentObject" value="31070939"/>
    <graphicsAlgorithm xsi:type="al:Rectangle" lineWidth="1" filled="false" lineVisible="false" transparency="0.0" width="125" height="64" x="400" y="155">
      <graphicsAlgorithmChildren xsi:type="al:RoundedRectangle" lineWidth="1" transparency="0.0" width="125" height="64" style="//@styles.1" cornerHeight="20" cornerWidth="20"/>
    </graphicsAlgorithm>
    <anchors xsi:type="pi:ChopboxAnchor" outgoingConnections="//@connections.0" incomingConnections="//@connections.7"/>
    <anchors xsi:type="pi:BoxRelativeAnchor" visible="true" active="true" referencedGraphicsAlgorithm="//@children.1/@graphicsAlgorithm/@graphicsAlgorithmChildren.0" relativeWidth="1.0" relativeHeight="0.51">
      <graphicsAlgorithm xsi:type="al:Ellipse" filled="false" lineVisible="false"/>
    </anchors>
    <children visible="true">
      <properties key="independentObject" value="31070939"/>
      <graphicsAlgorithm xsi:type="al:MultiText" lineWidth="1" filled="false" transparency="0.0" width="125" height="32" y="20" style="//@styles.1" font="//@fonts.0" horizontalAlignment="ALIGNMENT_CENTER" value="&#x7b2c;&#x4e00;&#x6b65;-&#x79d1;&#x5ba4;&#x4e3b;&#x4efb;&#x5ba1;&#x6838;"/>
    </children>
    <children visible="true">
      <graphicsAlgorithm xsi:type="al:Image" lineWidth="1" transparency="0.0" width="16" height="16" x="5" y="5" id="org.activiti.designer.guiusertask" stretchH="false" stretchV="false" proportional="false"/>
    </children>
  </children>
  <children xsi:type="pi:ContainerShape" visible="true" active="true">
    <properties key="independentObject" value="479210944"/>
    <graphicsAlgorithm xsi:type="al:Polygon" lineWidth="1" filled="false" lineVisible="false" transparency="0.0" width="40" height="40" x="590" y="166">
      <graphicsAlgorithmChildren xsi:type="al:Polygon" lineWidth="1" filled="true" transparency="0.0" width="40" height="40" style="//@styles.0">
        <points y="20"/>
        <points x="20"/>
        <points x="40" y="20"/>
        <points x="20" y="40"/>
        <points y="20"/>
      </graphicsAlgorithmChildren>
      <points y="20"/>
      <points x="20"/>
      <points x="40" y="20"/>
      <points x="20" y="40"/>
      <points y="20"/>
    </graphicsAlgorithm>
    <anchors xsi:type="pi:ChopboxAnchor" outgoingConnections="//@connections.3 //@connections.9" incomingConnections="//@connections.0"/>
    <anchors xsi:type="pi:BoxRelativeAnchor" visible="true" active="true" referencedGraphicsAlgorithm="//@children.2/@graphicsAlgorithm/@graphicsAlgorithmChildren.0" relativeWidth="0.51" relativeHeight="0.1">
      <graphicsAlgorithm xsi:type="al:Ellipse" filled="false" lineVisible="false"/>
    </anchors>
    <anchors xsi:type="pi:ChopboxAnchor"/>
    <anchors xsi:type="pi:BoxRelativeAnchor" visible="true" active="true" referencedGraphicsAlgorithm="//@children.2/@graphicsAlgorithm/@graphicsAlgorithmChildren.0" relativeWidth="0.51" relativeHeight="0.93">
      <graphicsAlgorithm xsi:type="al:Ellipse" filled="false" lineVisible="false"/>
    </anchors>
    <children visible="true">
      <graphicsAlgorithm xsi:type="al:Polyline" lineWidth="5" filled="false" transparency="0.0" style="//@styles.0">
        <points x="30" y="10"/>
        <points x="10" y="30"/>
      </graphicsAlgorithm>
    </children>
    <children visible="true">
      <graphicsAlgorithm xsi:type="al:Polyline" lineWidth="5" filled="false" transparency="0.0" style="//@styles.0">
        <points x="10" y="10"/>
        <points x="30" y="30"/>
      </graphicsAlgorithm>
    </children>
  </children>
  <children xsi:type="pi:ContainerShape" visible="true" active="true">
    <properties key="independentObject" value="896649754"/>
    <graphicsAlgorithm xsi:type="al:Ellipse" lineWidth="1" filled="false" lineVisible="false" transparency="0.0" width="35" height="35" x="870" y="256">
      <graphicsAlgorithmChildren xsi:type="al:Ellipse" lineWidth="3" transparency="0.0" width="35" height="35" style="//@styles.0"/>
    </graphicsAlgorithm>
    <anchors xsi:type="pi:ChopboxAnchor" incomingConnections="//@connections.4 //@connections.9"/>
  </children>
  <children xsi:type="pi:ContainerShape" visible="true" active="true">
    <properties key="independentObject" value="820442946"/>
    <graphicsAlgorithm xsi:type="al:Rectangle" lineWidth="1" filled="false" lineVisible="false" transparency="0.0" width="125" height="71" x="400" y="323">
      <graphicsAlgorithmChildren xsi:type="al:RoundedRectangle" lineWidth="1" transparency="0.0" width="125" height="71" style="//@styles.1" cornerHeight="20" cornerWidth="20"/>
    </graphicsAlgorithm>
    <anchors xsi:type="pi:ChopboxAnchor" outgoingConnections="//@connections.1" incomingConnections="//@connections.6"/>
    <anchors xsi:type="pi:BoxRelativeAnchor" visible="true" active="true" referencedGraphicsAlgorithm="//@children.4/@graphicsAlgorithm/@graphicsAlgorithmChildren.0" relativeWidth="1.0" relativeHeight="0.51">
      <graphicsAlgorithm xsi:type="al:Ellipse" filled="false" lineVisible="false"/>
    </anchors>
    <children visible="true">
      <properties key="independentObject" value="820442946"/>
      <graphicsAlgorithm xsi:type="al:MultiText" lineWidth="1" filled="false" transparency="0.0" width="125" height="39" y="20" style="//@styles.1" font="//@fonts.0" horizontalAlignment="ALIGNMENT_CENTER" value="&#x7b2c;&#x4e00;&#x6b65;-&#x6240;&#x9886;&#x5bfc;&#x5ba1;&#x6838;"/>
    </children>
    <children visible="true">
      <graphicsAlgorithm xsi:type="al:Image" lineWidth="1" transparency="0.0" width="16" height="16" x="5" y="5" id="org.activiti.designer.guiusertask" stretchH="false" stretchV="false" proportional="false"/>
    </children>
  </children>
  <children xsi:type="pi:ContainerShape" visible="true" active="true">
    <properties key="independentObject" value="867722033"/>
    <graphicsAlgorithm xsi:type="al:Polygon" lineWidth="1" filled="false" lineVisible="false" transparency="0.0" width="40" height="40" x="591" y="338">
      <graphicsAlgorithmChildren xsi:type="al:Polygon" lineWidth="1" filled="true" transparency="0.0" width="40" height="40" style="//@styles.0">
        <points y="20"/>
        <points x="20"/>
        <points x="40" y="20"/>
        <points x="20" y="40"/>
        <points y="20"/>
      </graphicsAlgorithmChildren>
      <points y="20"/>
      <points x="20"/>
      <points x="40" y="20"/>
      <points x="20" y="40"/>
      <points y="20"/>
    </graphicsAlgorithm>
    <anchors xsi:type="pi:ChopboxAnchor" outgoingConnections="//@connections.2 //@connections.4" incomingConnections="//@connections.1"/>
    <anchors xsi:type="pi:BoxRelativeAnchor" visible="true" active="true" referencedGraphicsAlgorithm="//@children.5/@graphicsAlgorithm/@graphicsAlgorithmChildren.0" relativeWidth="0.51" relativeHeight="0.1">
      <graphicsAlgorithm xsi:type="al:Ellipse" filled="false" lineVisible="false"/>
    </anchors>
    <anchors xsi:type="pi:ChopboxAnchor"/>
    <anchors xsi:type="pi:BoxRelativeAnchor" visible="true" active="true" referencedGraphicsAlgorithm="//@children.5/@graphicsAlgorithm/@graphicsAlgorithmChildren.0" relativeWidth="0.51" relativeHeight="0.93">
      <graphicsAlgorithm xsi:type="al:Ellipse" filled="false" lineVisible="false"/>
    </anchors>
    <children visible="true">
      <graphicsAlgorithm xsi:type="al:Polyline" lineWidth="5" filled="false" transparency="0.0" style="//@styles.0">
        <points x="30" y="10"/>
        <points x="10" y="30"/>
      </graphicsAlgorithm>
    </children>
    <children visible="true">
      <graphicsAlgorithm xsi:type="al:Polyline" lineWidth="5" filled="false" transparency="0.0" style="//@styles.0">
        <points x="10" y="10"/>
        <points x="30" y="30"/>
      </graphicsAlgorithm>
    </children>
  </children>
  <children xsi:type="pi:ContainerShape" visible="true" active="true">
    <properties key="independentObject" value="1257369729"/>
    <graphicsAlgorithm xsi:type="al:Rectangle" lineWidth="1" filled="false" lineVisible="false" transparency="0.0" width="105" height="55" x="401" y="246">
      <graphicsAlgorithmChildren xsi:type="al:RoundedRectangle" lineWidth="1" transparency="0.0" width="105" height="55" style="//@styles.1" cornerHeight="20" cornerWidth="20"/>
    </graphicsAlgorithm>
    <anchors xsi:type="pi:ChopboxAnchor" outgoingConnections="//@connections.8" incomingConnections="//@connections.2 //@connections.3"/>
    <anchors xsi:type="pi:BoxRelativeAnchor" visible="true" active="true" referencedGraphicsAlgorithm="//@children.6/@graphicsAlgorithm/@graphicsAlgorithmChildren.0" relativeWidth="1.0" relativeHeight="0.51">
      <graphicsAlgorithm xsi:type="al:Ellipse" filled="false" lineVisible="false"/>
    </anchors>
    <children visible="true">
      <properties key="independentObject" value="1257369729"/>
      <graphicsAlgorithm xsi:type="al:MultiText" lineWidth="1" filled="false" transparency="0.0" width="105" height="23" y="20" style="//@styles.1" font="//@fonts.0" horizontalAlignment="ALIGNMENT_CENTER" value="&#x7533;&#x8bf7;&#x4eba;&#x8ba2;&#x6b63;"/>
    </children>
    <children visible="true">
      <graphicsAlgorithm xsi:type="al:Image" lineWidth="1" transparency="0.0" width="16" height="16" x="5" y="5" id="org.activiti.designer.guiusertask" stretchH="false" stretchV="false" proportional="false"/>
    </children>
  </children>
  <children xsi:type="pi:ContainerShape" visible="true" active="true">
    <properties key="independentObject" value="580513560"/>
    <graphicsAlgorithm xsi:type="al:Polygon" lineWidth="1" filled="false" lineVisible="false" transparency="0.0" width="40" height="40" x="310" y="253">
      <graphicsAlgorithmChildren xsi:type="al:Polygon" lineWidth="1" filled="true" transparency="0.0" width="40" height="40" style="//@styles.0">
        <points y="20"/>
        <points x="20"/>
        <points x="40" y="20"/>
        <points x="20" y="40"/>
        <points y="20"/>
      </graphicsAlgorithmChildren>
      <points y="20"/>
      <points x="20"/>
      <points x="40" y="20"/>
      <points x="20" y="40"/>
      <points y="20"/>
    </graphicsAlgorithm>
    <anchors xsi:type="pi:ChopboxAnchor" outgoingConnections="//@connections.6 //@connections.7" incomingConnections="//@connections.5 //@connections.8"/>
    <anchors xsi:type="pi:BoxRelativeAnchor" visible="true" active="true" referencedGraphicsAlgorithm="//@children.7/@graphicsAlgorithm/@graphicsAlgorithmChildren.0" relativeWidth="0.51" relativeHeight="0.1">
      <graphicsAlgorithm xsi:type="al:Ellipse" filled="false" lineVisible="false"/>
    </anchors>
    <anchors xsi:type="pi:ChopboxAnchor"/>
    <anchors xsi:type="pi:BoxRelativeAnchor" visible="true" active="true" referencedGraphicsAlgorithm="//@children.7/@graphicsAlgorithm/@graphicsAlgorithmChildren.0" relativeWidth="0.51" relativeHeight="0.93">
      <graphicsAlgorithm xsi:type="al:Ellipse" filled="false" lineVisible="false"/>
    </anchors>
    <children visible="true">
      <graphicsAlgorithm xsi:type="al:Polyline" lineWidth="5" filled="false" transparency="0.0" style="//@styles.0">
        <points x="30" y="10"/>
        <points x="10" y="30"/>
      </graphicsAlgorithm>
    </children>
    <children visible="true">
      <graphicsAlgorithm xsi:type="al:Polyline" lineWidth="5" filled="false" transparency="0.0" style="//@styles.0">
        <points x="10" y="10"/>
        <points x="30" y="30"/>
      </graphicsAlgorithm>
    </children>
  </children>
  <styles foreground="//@colors.2" lineWidth="20" id="EVENT">
    <renderingStyle>
      <adaptedGradientColoredAreas definedStyleId="bpmnEventStyle" gradientType="0">
        <adaptedGradientColoredAreas styleAdaption="0">
          <gradientColor>
            <start locationType="LOCATION_TYPE_ABSOLUTE_START" locationValue="0">
              <color red="250" green="251" blue="252"/>
            </start>
            <end locationType="LOCATION_TYPE_ABSOLUTE_END" locationValue="0">
              <color red="250" green="251" blue="252"/>
            </end>
          </gradientColor>
        </adaptedGradientColoredAreas>
        <adaptedGradientColoredAreas styleAdaption="0">
          <gradientColor>
            <start locationType="LOCATION_TYPE_ABSOLUTE_START" locationValue="0">
              <color red="229" green="229" blue="194"/>
            </start>
            <end locationType="LOCATION_TYPE_ABSOLUTE_END" locationValue="0">
              <color red="229" green="229" blue="194"/>
            </end>
          </gradientColor>
        </adaptedGradientColoredAreas>
        <adaptedGradientColoredAreas styleAdaption="0">
          <gradientColor>
            <start locationType="LOCATION_TYPE_ABSOLUTE_START" locationValue="0">
              <color red="229" green="229" blue="194"/>
            </start>
            <end locationType="LOCATION_TYPE_ABSOLUTE_END" locationValue="0">
              <color red="229" green="229" blue="194"/>
            </end>
          </gradientColor>
        </adaptedGradientColoredAreas>
      </adaptedGradientColoredAreas>
    </renderingStyle>
  </styles>
  <styles foreground="//@colors.2" lineWidth="20" id="TASK">
    <renderingStyle>
      <adaptedGradientColoredAreas definedStyleId="bpmnTaskStyle" gradientType="0">
        <adaptedGradientColoredAreas styleAdaption="0">
          <gradientColor>
            <start locationType="LOCATION_TYPE_ABSOLUTE_START" locationValue="0">
              <color red="250" green="251" blue="252"/>
            </start>
            <end locationType="LOCATION_TYPE_ABSOLUTE_END" locationValue="0">
              <color red="255" green="255" blue="204"/>
            </end>
          </gradientColor>
        </adaptedGradientColoredAreas>
        <adaptedGradientColoredAreas styleAdaption="0">
          <gradientColor>
            <start locationType="LOCATION_TYPE_ABSOLUTE_START" locationValue="0">
              <color red="229" green="229" blue="194"/>
            </start>
            <end locationType="LOCATION_TYPE_ABSOLUTE_END" locationValue="0">
              <color red="229" green="229" blue="194"/>
            </end>
          </gradientColor>
        </adaptedGradientColoredAreas>
        <adaptedGradientColoredAreas styleAdaption="0">
          <gradientColor>
            <start locationType="LOCATION_TYPE_ABSOLUTE_START" locationValue="0">
              <color red="229" green="229" blue="194"/>
            </start>
            <end locationType="LOCATION_TYPE_ABSOLUTE_END" locationValue="0">
              <color red="229" green="229" blue="194"/>
            </end>
          </gradientColor>
        </adaptedGradientColoredAreas>
      </adaptedGradientColoredAreas>
    </renderingStyle>
  </styles>
  <styles background="//@colors.2" foreground="//@colors.2" lineWidth="1" id="BPMN-POLYGON-ARROW"/>
  <connections xsi:type="pi:FreeFormConnection" visible="true" active="true" start="//@children.1/@anchors.0" end="//@children.2/@anchors.0">
    <properties key="independentObject" value="629585698"/>
    <graphicsAlgorithm xsi:type="al:Polyline" foreground="//@colors.2" lineWidth="1" filled="false" transparency="0.0"/>
    <connectionDecorators visible="true" active="true" location="0.5">
      <graphicsAlgorithm xsi:type="al:MultiText" lineWidth="1" filled="false" transparency="0.0" style="//@styles.1" font="//@fonts.0" verticalAlignment="ALIGNMENT_TOP"/>
    </connectionDecorators>
    <connectionDecorators visible="true" locationRelative="true" location="1.0">
      <graphicsAlgorithm xsi:type="al:Polygon" lineWidth="1" filled="true" transparency="0.0" style="//@styles.2">
        <points x="-10" y="-5" before="3" after="3"/>
        <points/>
        <points x="-10" y="5" before="3" after="3"/>
        <points x="-8" before="3" after="3"/>
      </graphicsAlgorithm>
    </connectionDecorators>
  </connections>
  <connections xsi:type="pi:FreeFormConnection" visible="true" active="true" start="//@children.4/@anchors.0" end="//@children.5/@anchors.0">
    <properties key="independentObject" value="581331703"/>
    <graphicsAlgorithm xsi:type="al:Polyline" foreground="//@colors.2" lineWidth="1" filled="false" transparency="0.0"/>
    <connectionDecorators visible="true" active="true" location="0.5">
      <graphicsAlgorithm xsi:type="al:MultiText" lineWidth="1" filled="false" transparency="0.0" style="//@styles.1" font="//@fonts.0" verticalAlignment="ALIGNMENT_TOP"/>
    </connectionDecorators>
    <connectionDecorators visible="true" locationRelative="true" location="1.0">
      <graphicsAlgorithm xsi:type="al:Polygon" lineWidth="1" filled="true" transparency="0.0" style="//@styles.2">
        <points x="-10" y="-5" before="3" after="3"/>
        <points/>
        <points x="-10" y="5" before="3" after="3"/>
        <points x="-8" before="3" after="3"/>
      </graphicsAlgorithm>
    </connectionDecorators>
  </connections>
  <connections xsi:type="pi:FreeFormConnection" visible="true" active="true" start="//@children.5/@anchors.0" end="//@children.6/@anchors.0">
    <properties key="independentObject" value="1669171879"/>
    <graphicsAlgorithm xsi:type="al:Polyline" foreground="//@colors.2" lineWidth="1" filled="false" transparency="0.0"/>
    <connectionDecorators visible="true" active="true" location="0.5">
      <graphicsAlgorithm xsi:type="al:MultiText" lineWidth="1" filled="false" transparency="0.0" width="32" height="18" style="//@styles.1" font="//@fonts.0" verticalAlignment="ALIGNMENT_TOP" value="&#x9a73;&#x56de;"/>
    </connectionDecorators>
    <connectionDecorators visible="true" locationRelative="true" location="1.0">
      <graphicsAlgorithm xsi:type="al:Polygon" lineWidth="1" filled="true" transparency="0.0" style="//@styles.2">
        <points x="-10" y="-5" before="3" after="3"/>
        <points/>
        <points x="-10" y="5" before="3" after="3"/>
        <points x="-8" before="3" after="3"/>
      </graphicsAlgorithm>
    </connectionDecorators>
    <bendpoints x="610" y="273"/>
  </connections>
  <connections xsi:type="pi:FreeFormConnection" visible="true" active="true" start="//@children.2/@anchors.0" end="//@children.6/@anchors.0">
    <properties key="independentObject" value="643244018"/>
    <graphicsAlgorithm xsi:type="al:Polyline" foreground="//@colors.2" lineWidth="1" filled="false" transparency="0.0"/>
    <connectionDecorators visible="true" active="true" location="0.5">
      <graphicsAlgorithm xsi:type="al:MultiText" lineWidth="1" filled="false" transparency="0.0" width="32" height="18" x="10" y="27" style="//@styles.1" font="//@fonts.0" verticalAlignment="ALIGNMENT_TOP" value="&#x9a73;&#x56de;"/>
    </connectionDecorators>
    <connectionDecorators visible="true" locationRelative="true" location="1.0">
      <graphicsAlgorithm xsi:type="al:Polygon" lineWidth="1" filled="true" transparency="0.0" style="//@styles.2">
        <points x="-10" y="-5" before="3" after="3"/>
        <points/>
        <points x="-10" y="5" before="3" after="3"/>
        <points x="-8" before="3" after="3"/>
      </graphicsAlgorithm>
    </connectionDecorators>
    <bendpoints x="611" y="272"/>
  </connections>
  <connections xsi:type="pi:FreeFormConnection" visible="true" active="true" start="//@children.5/@anchors.0" end="//@children.3/@anchors.0">
    <properties key="independentObject" value="1341282738"/>
    <graphicsAlgorithm xsi:type="al:Polyline" foreground="//@colors.2" lineWidth="1" filled="false" transparency="0.0"/>
    <connectionDecorators visible="true" active="true" location="0.5">
      <graphicsAlgorithm xsi:type="al:MultiText" lineWidth="1" filled="false" transparency="0.0" width="32" height="18" x="82" y="-27" style="//@styles.1" font="//@fonts.0" verticalAlignment="ALIGNMENT_TOP" value="&#x901a;&#x8fc7;"/>
    </connectionDecorators>
    <connectionDecorators visible="true" locationRelative="true" location="1.0">
      <graphicsAlgorithm xsi:type="al:Polygon" lineWidth="1" filled="true" transparency="0.0" style="//@styles.2">
        <points x="-10" y="-5" before="3" after="3"/>
        <points/>
        <points x="-10" y="5" before="3" after="3"/>
        <points x="-8" before="3" after="3"/>
      </graphicsAlgorithm>
    </connectionDecorators>
    <bendpoints x="887" y="358"/>
  </connections>
  <connections xsi:type="pi:FreeFormConnection" visible="true" active="true" start="//@children.0/@anchors.0" end="//@children.7/@anchors.0">
    <properties key="independentObject" value="111558003"/>
    <graphicsAlgorithm xsi:type="al:Polyline" foreground="//@colors.2" lineWidth="1" filled="false" transparency="0.0"/>
    <connectionDecorators visible="true" active="true" location="0.5">
      <graphicsAlgorithm xsi:type="al:MultiText" lineWidth="1" filled="false" transparency="0.0" style="//@styles.1" font="//@fonts.0" verticalAlignment="ALIGNMENT_TOP"/>
    </connectionDecorators>
    <connectionDecorators visible="true" locationRelative="true" location="1.0">
      <graphicsAlgorithm xsi:type="al:Polygon" lineWidth="1" filled="true" transparency="0.0" style="//@styles.2">
        <points x="-10" y="-5" before="3" after="3"/>
        <points/>
        <points x="-10" y="5" before="3" after="3"/>
        <points x="-8" before="3" after="3"/>
      </graphicsAlgorithm>
    </connectionDecorators>
  </connections>
  <connections xsi:type="pi:FreeFormConnection" visible="true" active="true" start="//@children.7/@anchors.0" end="//@children.4/@anchors.0">
    <properties key="independentObject" value="709145736"/>
    <graphicsAlgorithm xsi:type="al:Polyline" foreground="//@colors.2" lineWidth="1" filled="false" transparency="0.0"/>
    <connectionDecorators visible="true" active="true" location="0.5">
      <graphicsAlgorithm xsi:type="al:MultiText" lineWidth="1" filled="false" transparency="0.0" width="64" height="18" x="-49" y="57" style="//@styles.1" font="//@fonts.0" verticalAlignment="ALIGNMENT_TOP" value="&#x4e2d;&#x5c42;&#x9886;&#x5bfc;"/>
    </connectionDecorators>
    <connectionDecorators visible="true" locationRelative="true" location="1.0">
      <graphicsAlgorithm xsi:type="al:Polygon" lineWidth="1" filled="true" transparency="0.0" style="//@styles.2">
        <points x="-10" y="-5" before="3" after="3"/>
        <points/>
        <points x="-10" y="5" before="3" after="3"/>
        <points x="-8" before="3" after="3"/>
      </graphicsAlgorithm>
    </connectionDecorators>
    <bendpoints x="330" y="358"/>
  </connections>
  <connections xsi:type="pi:FreeFormConnection" visible="true" active="true" start="//@children.7/@anchors.0" end="//@children.1/@anchors.0">
    <properties key="independentObject" value="1306728595"/>
    <graphicsAlgorithm xsi:type="al:Polyline" foreground="//@colors.2" lineWidth="1" filled="false" transparency="0.0"/>
    <connectionDecorators visible="true" active="true" location="0.5">
      <graphicsAlgorithm xsi:type="al:MultiText" lineWidth="1" filled="false" transparency="0.0" width="32" height="18" x="-20" y="-86" style="//@styles.1" font="//@fonts.0" verticalAlignment="ALIGNMENT_TOP" value="&#x804c;&#x5de5;"/>
    </connectionDecorators>
    <connectionDecorators visible="true" locationRelative="true" location="1.0">
      <graphicsAlgorithm xsi:type="al:Polygon" lineWidth="1" filled="true" transparency="0.0" style="//@styles.2">
        <points x="-10" y="-5" before="3" after="3"/>
        <points/>
        <points x="-10" y="5" before="3" after="3"/>
        <points x="-8" before="3" after="3"/>
      </graphicsAlgorithm>
    </connectionDecorators>
    <bendpoints x="330" y="186"/>
  </connections>
  <connections xsi:type="pi:FreeFormConnection" visible="true" active="true" start="//@children.6/@anchors.0" end="//@children.7/@anchors.0">
    <properties key="independentObject" value="1738979889"/>
    <graphicsAlgorithm xsi:type="al:Polyline" foreground="//@colors.2" lineWidth="1" filled="false" transparency="0.0"/>
    <connectionDecorators visible="true" active="true" location="0.5">
      <graphicsAlgorithm xsi:type="al:MultiText" lineWidth="1" filled="false" transparency="0.0" style="//@styles.1" font="//@fonts.0" verticalAlignment="ALIGNMENT_TOP"/>
    </connectionDecorators>
    <connectionDecorators visible="true" locationRelative="true" location="1.0">
      <graphicsAlgorithm xsi:type="al:Polygon" lineWidth="1" filled="true" transparency="0.0" style="//@styles.2">
        <points x="-10" y="-5" before="3" after="3"/>
        <points/>
        <points x="-10" y="5" before="3" after="3"/>
        <points x="-8" before="3" after="3"/>
      </graphicsAlgorithm>
    </connectionDecorators>
  </connections>
  <connections xsi:type="pi:FreeFormConnection" visible="true" active="true" start="//@children.2/@anchors.0" end="//@children.3/@anchors.0">
    <properties key="independentObject" value="1005531806"/>
    <graphicsAlgorithm xsi:type="al:Polyline" foreground="//@colors.2" lineWidth="1" filled="false" transparency="0.0"/>
    <connectionDecorators visible="true" active="true" location="0.5">
      <graphicsAlgorithm xsi:type="al:MultiText" lineWidth="1" filled="false" transparency="0.0" style="//@styles.1" font="//@fonts.0" verticalAlignment="ALIGNMENT_TOP" value=""/>
    </connectionDecorators>
    <connectionDecorators visible="true" locationRelative="true" location="1.0">
      <graphicsAlgorithm xsi:type="al:Polygon" lineWidth="1" filled="true" transparency="0.0" style="//@styles.2">
        <points x="-10" y="-5" before="3" after="3"/>
        <points/>
        <points x="-10" y="5" before="3" after="3"/>
        <points x="-8" before="3" after="3"/>
      </graphicsAlgorithm>
    </connectionDecorators>
    <bendpoints x="610" y="273"/>
  </connections>
  <colors red="227" green="238" blue="249"/>
  <colors red="255" green="255" blue="255"/>
  <colors/>
  <fonts name="Arial" size="8"/>
</pi:Diagram>
