package cn.com.sinosoft.os.beijingexitapplynew.service;

import ie.bsp.frame.model.SignInfo;
import cn.com.sinosoft.os.beijingexitapplynew.model.BeijingExitApplyNew;

/**
 * 出京申请 - service接口.
 *
 * <AUTHOR>
 * @date: 2025/08/02 16:00:00
 * @version V1.0
 */
public interface BeijingExitApplyNewService {

	/**
	 * 出京申请 - 获取单条数据.
	 *
	 * <AUTHOR>
	 * @param id 主键
	 * @return 出京申请 数据
	 */
	BeijingExitApplyNew get(String id);

	/**
	 * 出京申请 - 删除.
	 *
	 * <AUTHOR>
	 * @param id 主键
	 */
	void delete(String id);

	/**
	 * 出京申请 - 保存.
	 *
	 * <AUTHOR>
	 * @param result 出京申请 数据
	 */
	void save(BeijingExitApplyNew result);

	/**
	 * 出京申请 - 修改.
	 *
	 * <AUTHOR>
	 * @param result 出京申请 数据
	 */
	void edit(BeijingExitApplyNew result);

	/**
	 * 根据流程ID获取数据.
	 *
	 * @param piId 流程ID
	 * @return 出京申请数据
	 */
	BeijingExitApplyNew getPiId(String piId);

	/**
	 * 获取签名信息.
	 *
	 * @param id 主键
	 * @return 签名信息
	 */
	SignInfo getSignInfo(String id);

	/**
	 * 申请提交.
	 *
	 * @param id 主键
	 * @param parama 参数
	 * @param taskid 任务ID
	 * @param piId 流程ID
	 */
	void appSubmit(String id, String parama, String taskid, String piId);

	/**
	 * 生成申请编号.
	 *
	 * @return 申请编号
	 */
	String numberGenerate();
}
